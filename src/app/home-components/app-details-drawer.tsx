"use client";

import React from "react";
import { Drawer } from "vaul";
import { Phone, Shield } from "lucide-react";
import { App } from "@/core.constants";
import { SecurityFeature } from "@/app/home-components/security-feature";
import { AppHeader } from "@/app/home-components/app-header";
import { QuickActions } from "@/app/home-components/quick-actions";

interface AppDetailsDrawerProps {
  app: App | null;
  isOpen: boolean;
  onClose: () => void;
}

const securityFeatures = [
  {
    key: "yubikeys",
    icon: Shield,
    title: "Yubikey Support",
    description: "Hardware security key authentication",
    color: "bg-blue-500",
  },
  {
    key: "phoneNumber",
    icon: Phone,
    title: "Phone Number Required",
    description: "Phone number dependency for 2FA",
    getColor: (enabled: boolean) => (enabled ? "bg-red-500" : "bg-green-500"),
  },
  {
    key: "backup",
    icon: Shield,
    title: "Backup Codes",
    description: "Recovery codes for emergency access",
    color: "bg-purple-500",
  },
] as const;

export const AppDetailsDrawer = ({
  app,
  isOpen,
  onClose,
}: AppDetailsDrawerProps) => {
  if (!app) return null;

  return (
    <Drawer.Root open={isOpen} onOpenChange={onClose}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
        <Drawer.Content className="bg-white dark:bg-neutral-900 flex flex-col rounded-t-[10px] h-[85vh] mt-24 fixed bottom-0 left-0 right-0 z-50">
          <Drawer.Title></Drawer.Title>
          <div className="p-4 bg-white dark:bg-neutral-900 rounded-t-[10px] flex-1 overflow-y-auto pb-[100px]">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-neutral-300 dark:bg-neutral-600 mb-6" />

            <AppHeader app={app} />

            <div className="space-y-6">
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <Shield className="w-5 h-5 text-blue-500" />
                  <h2 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                    Security Features
                  </h2>
                </div>
                <div className="space-y-3">
                  {securityFeatures.map((feature) => {
                    const featureData = app[feature.key];
                    const enabled = featureData?.enabled ?? false;
                    const color =
                      "getColor" in feature
                        ? feature.getColor(enabled)
                        : feature.color;

                    return (
                      <SecurityFeature
                        key={feature.key}
                        icon={feature.icon}
                        title={feature.title}
                        enabled={enabled}
                        link={featureData?.link}
                        description={feature.description}
                        color={color}
                      />
                    );
                  })}
                </div>
              </div>

              <QuickActions app={app} />
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
};
