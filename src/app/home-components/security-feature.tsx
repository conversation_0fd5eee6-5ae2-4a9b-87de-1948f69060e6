"use client";

import React from "react";
import Link from "next/link";
import { CheckCircle, XCircle, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface SecurityFeatureProps {
  icon: React.ElementType;
  title: string;
  enabled: boolean;
  link?: string;
  description: string;
  color: string;
}

export const SecurityFeature = ({
  icon: Icon,
  title,
  enabled,
  link,
  description,
  color,
}: SecurityFeatureProps) => (
  <div className="rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 p-4">
    <div className="flex items-start space-x-3 mb-3">
      <div className={`rounded-lg p-2 ${color}`}>
        <Icon className="w-4 h-4 text-white" />
      </div>
      <div className="flex-1">
        <h4 className="font-semibold text-sm text-neutral-900 dark:text-neutral-100 mb-1">
          {title}
        </h4>
        <p className="text-xs text-neutral-600 dark:text-neutral-400 leading-relaxed">
          {description}
        </p>
      </div>
    </div>

    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        {enabled ? (
          <CheckCircle className="w-4 h-4 text-green-500" />
        ) : (
          <XCircle className="w-4 h-4 text-red-500" />
        )}
        <Badge
          variant={enabled ? "default" : "destructive"}
          className={`text-xs px-2 py-1 ${
            enabled
              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
              : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
          }`}
        >
          {enabled ? "Supported" : "Not Available"}
        </Badge>
      </div>

      {enabled && link && (
        <Button asChild size="sm" variant="outline" className="h-7 text-xs">
          <Link href={link} target="_blank" className="flex items-center">
            <ExternalLink className="w-3 h-3 mr-1" />
            Configure
          </Link>
        </Button>
      )}
    </div>
  </div>
);
