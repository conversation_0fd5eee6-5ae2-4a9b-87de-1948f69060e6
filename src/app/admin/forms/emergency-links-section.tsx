"use client";

import React from "react";
import {
  <PERSON><PERSON>ormRegister,
  FieldErrors,
  FieldArrayWithId,
  UseFieldArrayAppend,
  UseFieldArrayRemove,
} from "react-hook-form";
import { Plus, Trash2 } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { AppFormData } from "./basic-info-section";



interface EmergencyLinksSectionProps {
  register: UseFormRegister<AppFormData>;
  errors: FieldErrors<AppFormData>;
  emergencyLinks: FieldArrayWithId<AppFormData, "emergency.links", "id">[];
  appendEmergencyLink: UseFieldArrayAppend<AppFormData, "emergency.links">;
  removeEmergencyLink: UseFieldArrayRemove;
}

export const EmergencyLinksSection = ({
  register,
  errors,
  emergencyLinks,
  appendEmergencyLink,
  removeEmergencyLink,
}: EmergencyLinksSectionProps) => {
  return (
    <div className="bg-gray-50 dark:bg-gray-800 border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h4 className="font-semibold text-gray-900 dark:text-gray-100">
          🚨 Emergency Links
        </h4>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => appendEmergencyLink({ link: "", description: "" })}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Link
        </Button>
      </div>
      <div className="space-y-4">
        {emergencyLinks.map((field, index) => (
          <div
            key={field.id}
            className="bg-white dark:bg-gray-700 border rounded-lg p-4"
          >
            <div className="flex items-center gap-2 mb-4">
              <div className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-medium">
                {index + 1}
              </div>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                Emergency Link {index + 1}
              </span>
              {emergencyLinks.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeEmergencyLink(index)}
                  className="ml-auto"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor={`emergency.links.${index}.link`}
                  className="text-sm font-medium"
                >
                  Link
                </Label>
                <Input
                  {...register(`emergency.links.${index}.link`)}
                  placeholder="Emergency link URL"
                  className="h-11"
                />
                {errors.emergency?.links?.[index]?.link && (
                  <p className="text-sm text-red-600">
                    {errors.emergency.links[index]?.link?.message}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor={`emergency.links.${index}.description`}
                  className="text-sm font-medium"
                >
                  Description
                </Label>
                <Input
                  {...register(`emergency.links.${index}.description`)}
                  placeholder="Link description"
                  className="h-11"
                />
                {errors.emergency?.links?.[index]?.description && (
                  <p className="text-sm text-red-600">
                    {errors.emergency.links[index]?.description?.message}
                  </p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
