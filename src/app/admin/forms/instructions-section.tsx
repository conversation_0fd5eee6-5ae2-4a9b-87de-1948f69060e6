"use client";

import React from "react";
import {
  UseFormRegister,
  FieldErrors,
  UseFormWatch,
  FieldArrayWithId,
  UseFieldArrayAppend,
  UseFieldArrayRemove,
} from "react-hook-form";
import { Plus, Trash2 } from "lucide-react";
import Image from "next/image";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { FileInput } from "@/components/ui/file-input";
import { AppFormData } from "./basic-info-section";

interface InstructionsSectionProps {
  register: UseFormRegister<AppFormData>;
  errors: FieldErrors<AppFormData>;
  watch: UseFormWatch<AppFormData>;
  instructions: FieldArrayWithId<AppFormData, "emergency.instructions", "id">[];
  appendInstruction: UseFieldArrayAppend<AppFormData, "emergency.instructions">;
  removeInstruction: UseFieldArrayRemove;
  onImageUpload: (file: File, index: number) => Promise<void>;
  uploadingImages: Set<number>;
}

export const InstructionsSection = ({
  register,
  errors,
  watch,
  instructions,
  appendInstruction,
  removeInstruction,
  onImageUpload,
  uploadingImages,
}: InstructionsSectionProps) => {
  return (
    <div className="bg-gray-50 dark:bg-gray-800 border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h4 className="font-semibold text-gray-900 dark:text-gray-100">
          📋 Instructions
        </h4>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => appendInstruction({ description: "", imageLink: "" })}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Instruction
        </Button>
      </div>
      <div className="space-y-4">
        {instructions.map((field, index) => (
          <div
            key={field.id}
            className="bg-white dark:bg-gray-700 border rounded-lg p-4"
          >
            <div className="flex items-center gap-2 mb-4">
              <div className="w-6 h-6 bg-green-100 text-green-800 rounded-full flex items-center justify-center text-sm font-medium">
                {index + 1}
              </div>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                Instruction {index + 1}
              </span>
              {instructions.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeInstruction(index)}
                  className="ml-auto"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label
                  htmlFor={`emergency.instructions.${index}.description`}
                  className="text-sm font-medium"
                >
                  Instruction Description
                </Label>
                <Input
                  {...register(`emergency.instructions.${index}.description`)}
                  placeholder="Instruction description"
                  className="h-11"
                />
                {errors.emergency?.instructions?.[index]?.description && (
                  <p className="text-sm text-red-600">
                    {errors.emergency.instructions[index]?.description?.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Instruction Image (Optional)
                </Label>
                <FileInput
                  onFileSelect={(file) => onImageUpload(file, index)}
                  accept="image/*"
                  disabled={uploadingImages.has(index)}
                  loading={uploadingImages.has(index)}
                  currentFile={watch(
                    `emergency.instructions.${index}.imageLink`
                  )}
                  placeholder="Upload instruction image"
                />
                {watch(`emergency.instructions.${index}.imageLink`) && (
                  <div className="mt-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
                    <p className="text-sm font-medium mb-2">Preview:</p>
                    <Image
                      src={watch(`emergency.instructions.${index}.imageLink`)!}
                      alt="Instruction"
                      width={300}
                      height={128}
                      className="max-w-xs max-h-32 object-contain border rounded-lg bg-white"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
