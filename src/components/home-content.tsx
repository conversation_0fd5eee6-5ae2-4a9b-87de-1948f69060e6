"use client";

import React, { useState, useEffect } from "react";
import { BentoGrid, BentoCard } from "@/components/ui/bento-grid";
import { AppDetailsModal } from "@/app/home-components/app-details-modal";
import { AppDetailsDrawer } from "@/app/home-components/app-details-drawer";
import { FilterDrawer } from "@/components/filter-drawer";
import { Button } from "@/components/ui/button";
import { App, AppType, APP_TYPE_TEXT } from "@/core.constants";
import { getApps } from "@/api/app-api";
import { TextEffect } from "@/components/motion-primitives/text-effect";
import { AnimatedGroup } from "@/components/motion-primitives/animated-group";
import { Shield, Filter } from "lucide-react";

const transitionVariants = {
  item: {
    hidden: {
      opacity: 0,
      filter: "blur(12px)",
      y: 12,
    },
    visible: {
      opacity: 1,
      filter: "blur(0px)",
      y: 0,
      transition: {
        type: "spring",
        bounce: 0.3,
        duration: 1.5,
      },
    },
  },
};

export const HomeContent = () => {
  const [apps, setApps] = useState<App[]>([]);
  const [filteredApps, setFilteredApps] = useState<App[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<AppType | "all">("all");
  const [loading, setLoading] = useState(true);
  const [selectedApp, setSelectedApp] = useState<App | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const fetchApps = async () => {
      try {
        setLoading(true);
        const result = await getApps(50);
        setApps(result.apps);
        setFilteredApps(result.apps);
      } catch (error) {
        console.error("Error fetching apps:", error);
        setApps([]);
        setFilteredApps([]);
      } finally {
        setLoading(false);
      }
    };

    fetchApps();
  }, []);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  useEffect(() => {
    if (selectedFilter === "all") {
      setFilteredApps(apps);
    } else {
      setFilteredApps(apps.filter((app) => app.type === selectedFilter));
    }
  }, [selectedFilter, apps]);

  const filterButtons = [
    { key: "all", label: "All Apps" },
    ...Object.entries(APP_TYPE_TEXT).map(([key, value]) => ({
      key: key as AppType,
      label: value,
    })),
  ];

  const handleAppClick = (app: App) => {
    setSelectedApp(app);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedApp(null);
  };

  return (
    <main className="min-h-screen bg-background">
      <section className="relative pt-24 pb-16">
        <div className="absolute inset-0 -z-10 size-full [background:radial-gradient(125%_125%_at_50%_100%,transparent_0%,var(--color-background)_75%)]"></div>
        <div className="mx-auto max-w-7xl px-6">
          <div className="text-center">
            <TextEffect
              preset="fade-in-blur"
              speedSegment={0.3}
              as="h1"
              className="mt-8 max-w-4xl mx-auto text-balance text-5xl font-medium md:text-6xl lg:mt-16"
            >
              SafeCheck by Opsek
            </TextEffect>
            <div className="mt-4">
              <a
                href="https://www.opsek.io/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-lg text-muted-foreground hover:text-foreground transition-colors underline"
              >
                Visit Opsek →
              </a>
            </div>
            <TextEffect
              per="line"
              preset="fade-in-blur"
              speedSegment={0.3}
              delay={0.5}
              as="p"
              className="mt-8 max-w-3xl mx-auto text-pretty text-lg text-muted-foreground"
            >
              Check where you can use YubiKeys, remove your phone number, set up
              a backup plan, or revoke sessions in an emergency.
            </TextEffect>
          </div>
        </div>
      </section>

      <section className="pb-8 hidden md:block">
        <div className="mx-auto max-w-7xl px-6">
          <AnimatedGroup
            variants={{
              container: {
                visible: {
                  transition: {
                    staggerChildren: 0.05,
                    delayChildren: 0.75,
                  },
                },
              },
              ...transitionVariants,
            }}
            className="flex flex-wrap gap-2 justify-center"
          >
            {filterButtons.map((filter) => (
              <Button
                key={filter.key}
                variant={selectedFilter === filter.key ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedFilter(filter.key as AppType | "all")}
                className="text-sm cursor-pointer"
              >
                {filter.label}
              </Button>
            ))}
          </AnimatedGroup>
        </div>
      </section>

      <section className="pb-24 md:pb-16">
        <div className="mx-auto max-w-7xl px-6">
          {loading ? (
            <div className="flex justify-center items-center py-16">
              <div className="text-muted-foreground">Loading apps...</div>
            </div>
          ) : filteredApps.length > 0 ? (
            <BentoGrid className="mx-auto">
              {filteredApps.map((app) => {
                // All cards same size to avoid gaps
                const getCardSize = () => {
                  return "col-span-1 row-span-1";
                };

                return (
                  <BentoCard
                    key={app.id}
                    name={app.name}
                    className={`cursor-pointer ${getCardSize()}`}
                    logo={app.appLogo}
                    Icon={Shield}
                    href="#"
                    cta="View Details"
                    type={APP_TYPE_TEXT[app.type]}
                    has2FA={app.yubikeys.enabled}
                    hasBackup={app.backup.enabled}
                    hasPhoneRequired={app.phoneNumber.enabled}
                    hasEmergencyAccess={app.emergency.links.length > 0}
                    onClick={() => handleAppClick(app)}
                  />
                );
              })}
            </BentoGrid>
          ) : (
            <div className="text-center py-16">
              <div className="text-muted-foreground">
                {selectedFilter === "all"
                  ? "No apps found. Add some apps to get started."
                  : `No apps found for ${
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      // @ts-expect-error
                      selectedFilter !== "all"
                        ? APP_TYPE_TEXT[selectedFilter] || selectedFilter
                        : ""
                    }.`}
              </div>
            </div>
          )}
        </div>
      </section>

      {isMobile && (
        <div className="fixed bottom-0 left-0 right-0 z-40 p-4">
          <div className="flex justify-center">
            <Button
              onClick={() => setIsFilterDrawerOpen(true)}
              className="h-10 px-6 text-sm font-medium shadow-lg shadow-black/10 dark:shadow-black/20"
              variant="default"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
              {selectedFilter !== "all" && (
                <span className="ml-2 px-2 py-0.5 bg-background/20 rounded-full text-xs">
                  {filterButtons.find((f) => f.key === selectedFilter)?.label}
                </span>
              )}
            </Button>
          </div>
        </div>
      )}

      {isMobile ? (
        <AppDetailsDrawer
          onClose={handleCloseModal}
          app={selectedApp}
          isOpen={isModalOpen}
        />
      ) : (
        <AppDetailsModal
          app={selectedApp}
          onClose={handleCloseModal}
          isOpen={isModalOpen}
        />
      )}

      {isMobile && (
        <FilterDrawer
          isOpen={isFilterDrawerOpen}
          selectedFilter={selectedFilter}
          onClose={() => setIsFilterDrawerOpen(false)}
          onFilterChange={setSelectedFilter}
          filterButtons={filterButtons}
        />
      )}
    </main>
  );
};
