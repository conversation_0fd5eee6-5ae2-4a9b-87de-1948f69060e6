import { ComponentPropsWithoutRef, ReactNode } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import { cn } from "@/lib/utils";
import { ArrowRight } from "lucide-react";

interface BentoGridProps extends ComponentPropsWithoutRef<"div"> {
  children: ReactNode;
  className?: string;
}

interface BentoCardProps extends ComponentPropsWithoutRef<"div"> {
  name: string;
  className: string;
  logo?: string;
  Icon?: React.ElementType;
  href: string;
  cta: string;
  type?: string;
  has2FA?: boolean;
  hasBackup?: boolean;
  hasPhoneRequired?: boolean;
  hasEmergencyAccess?: boolean;
}

const BentoGrid = ({ children, className, ...props }: BentoGridProps) => {
  return (
    <div
      className={cn(
        "grid w-full auto-rows-[12.5rem] gap-4",
        "[grid-template-columns:repeat(auto-fit,minmax(280px,1fr))]",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

const BentoCard = ({
  name,
  className,
  logo,
  Icon,
  href,
  cta,
  type,
  has2FA,
  hasBackup,
  hasPhoneRequired,
  hasEmergencyAccess,
  ...props
}: BentoCardProps) => (
  <div
    key={name}
    className={cn(
      "group relative flex flex-col justify-between  rounded-xl",
      // light styles
      "bg-background [box-shadow:0_0_0_1px_rgba(0,0,0,.03),0_2px_4px_rgba(0,0,0,.05),0_12px_24px_rgba(0,0,0,.05)]",
      // dark styles
      "transform-gpu dark:bg-background dark:[border:1px_solid_rgba(255,255,255,.1)] dark:[box-shadow:0_-20px_80px_-20px_#ffffff1f_inset]",
      className
    )}
    {...props}
  >
    <GlowingEffect
      spread={40}
      glow={true}
      disabled={false}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={2}
    />
    <div className="flex flex-col gap-3 p-4">
      <div className="flex items-center gap-3">
        {logo ? (
          <img
            src={logo}
            alt={`${name} logo`}
            className="h-8 w-8 rounded-lg object-contain"
          />
        ) : Icon ? (
          <Icon className="h-8 w-8 text-neutral-700 dark:text-neutral-300" />
        ) : null}
        <div>
          <h3 className="text-base font-semibold text-neutral-700 dark:text-neutral-300">
            {name}
          </h3>
          {type && (
            <span className="text-xs text-neutral-500 dark:text-neutral-400">
              {type}
            </span>
          )}
        </div>
      </div>

      <div className="flex gap-1 flex-wrap">
        {has2FA && (
          <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-700 dark:bg-green-900/20 dark:text-green-400">
            🔐 2FA
          </span>
        )}
        {hasBackup && (
          <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
            💾 Backup
          </span>
        )}
        {hasPhoneRequired && (
          <span className="inline-flex items-center rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-700 dark:bg-orange-900/20 dark:text-orange-400">
            📱 Phone
          </span>
        )}
        {hasEmergencyAccess && (
          <span className="inline-flex items-center rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-700 dark:bg-red-900/20 dark:text-red-400">
            🚨 Emergency
          </span>
        )}
      </div>
    </div>

    <div
      className={cn(
        "pointer-events-none absolute bottom-0 flex w-full translate-y-10 transform-gpu flex-row items-center p-4 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100"
      )}
    >
      <Button variant="ghost" asChild size="sm" className="pointer-events-auto">
        <a href={href}>
          {cta}
          <ArrowRight className="ms-2 h-4 w-4 rtl:rotate-180" />
        </a>
      </Button>
    </div>
    <div className="pointer-events-none absolute inset-0 transform-gpu transition-all duration-300 group-hover:bg-black/[.03] group-hover:dark:bg-neutral-800/10" />
  </div>
);

export { BentoCard, BentoGrid };
